# -*- coding:utf-8 -*-
"""
Author: BigCat (Modified for PaiLieWu by Gemini)
"""
import argparse
import os
import requests
import pandas as pd
from bs4 import BeautifulSoup
from loguru import logger
from typing import List

parser = argparse.ArgumentParser()
parser.add_argument('--start', type=int, help="开始期数 (可选，默认从第1期开始)")
parser.add_argument('--end', type=int, help="结束期数 (可选，默认到最新期)")
parser.add_argument('--count', type=int, help="爬取最新的N期数据 (可选，优先级高于start/end参数)")
parser.add_argument('--tail', type=int, help="只保存最后N期数据到文件 (可选，用于控制文件大小)")
args = parser.parse_args()


def get_url():
    """
    获取排列五的URL
    :return: url, path
    """
    url = "https://datachart.500.com/plw/history/"
    path = "newinc/history.php?start={}&end="
    return url, path


def get_current_number():
    """ 获取排列五最新一期数字
    :return: int
    """
    url, _ = get_url()
    r = requests.get("{}{}".format(url, "history.shtml"), verify=False)
    r.encoding = "gb2312"
    soup = BeautifulSoup(r.text, "lxml")
    current_num = soup.find("div", class_="wrap_datachart").find("input", id="end")["value"]
    return current_num


def spider(start: int, end: int, mode: str, tail: int = None):
    """ 爬取排列五历史数据
    :param start 开始一期
    :param end 最近一期
    :param mode 模式，train：训练模式，predict：预测模式（训练模式会保持文件）
    :param tail 只保存最后N期数据到文件
    """
    url, path = get_url()
    url = "{}{}{}".format(url, path.format(start), end)
    r = requests.get(url=url, verify=False)
    r.encoding = "gb2312"
    soup = BeautifulSoup(r.text, "lxml")
    
    # 检查tdata是否存在，如果不存在则尝试获取不同的id，或者直接从html中查找table
    # 对于排列五，页面结构可能有所不同，500wan的排列五历史数据表格id通常是'tdata'或'chartBody'
    trs = None
    try:
        trs = soup.find("tbody", attrs={"id": "tdata"}).find_all("tr")
    except AttributeError:
        # 如果id="tdata"找不到，尝试查找id="chartBody"，或者更通用的表格结构
        logger.warning("未能找到 id='tdata' 的tbody，尝试查找其他表格结构...")
        try:
            trs = soup.find("table", class_="chartTable").find("tbody").find_all("tr")
        except AttributeError:
            logger.error("无法找到包含历史数据的表格结构，请检查网页HTML结构是否已更改。")
            return pd.DataFrame() # 返回空DataFrame

    if not trs:
        logger.warning("没有找到任何表格行数据。")
        return pd.DataFrame() # 返回空DataFrame

    data = []
    for tr in trs:
        tds = tr.find_all("td")
        item = dict()
        
        # 获取期号
        item[u"期数"] = tds[0].get_text().strip()

        # 排列五：5个号码
        for i in range(5):
            item[u"号码_{}".format(i+1)] = tds[i+1].get_text().strip()
            
        # 获取日期（通常在最后一列或倒数第二列）
        try:
            # 尝试获取日期字段，如果没有则设为空
            # 对于500wan的排列五，日期通常在倒数第二列或者倒数第三列（包含销售额等信息）
            date_text = ""
            if len(tds) > 1: # 确保tds有足够的元素
                # 假设日期在倒数第二列或倒数第三列
                # 排列五页面，通常倒数第二列是销售额，倒数第三列是日期
                if len(tds) >= 3 and any(char in tds[-3].get_text().strip() for char in ['-', '/', '年', '月', '日']):
                    date_text = tds[-3].get_text().strip()
                elif len(tds) >= 2 and any(char in tds[-2].get_text().strip() for char in ['-', '/', '年', '月', '日']):
                    date_text = tds[-2].get_text().strip()
                elif any(char in tds[-1].get_text().strip() for char in ['-', '/', '年', '月', '日']):
                     date_text = tds[-1].get_text().strip()

            item[u"日期"] = date_text
        except Exception as e:
            logger.warning(f"获取日期失败，可能没有日期信息或格式不符: {e}")
            item[u"日期"] = ""
            
        data.append(item)

    if not data:
        logger.warning("没有成功解析到任何数据，请检查网页结构或期数范围。")
        return pd.DataFrame()

    df = pd.DataFrame(data)
    # 按期数排序，确保最新期号在最下面
    df["期数"] = df["期数"].astype(int)
    df = df.sort_values("期数")
    df["期数"] = df["期数"].astype(str)
    
    # 重新排列列的顺序
    columns_order = ["期数"]
    num_columns = 5  # 排列五固定5个号码
    for i in range(1, num_columns + 1):
        columns_order.append(f"号码_{i}")
    columns_order.append("日期")
    
    # 过滤掉不存在的列，避免KeyError
    df = df[[col for col in columns_order if col in df.columns]]
    
    if mode == "train":
        # 如果指定了tail参数，只保留最后N期数据
        if tail is not None and tail > 0:
            if len(df) > tail:
                df = df.tail(tail)
                logger.info(f"应用tail参数：只保存最后 {tail} 期数据")
        
        # 确保data目录存在
        os.makedirs("data", exist_ok=True)
        filename = "data/plw_data.csv"  # 排列五数据文件
        df.to_csv(filename, encoding="utf-8", index=False)
        return df
    elif mode == "predict":
        return df


def run(start: int = None, end: int = None, count: int = None, tail: int = None):
    """
    爬取排列五数据
    :param start: 开始期数 (可选)
    :param end: 结束期数 (可选)
    :param count: 爬取最新的N期数据 (可选，优先级高于start/end)
    :param tail: 只保存最后N期数据到文件 (可选)
    :return:
    """
    current_number = int(get_current_number())
    game_name = "排列五"
    
    # 如果指定了count参数，优先使用count
    if count is not None:
        if count <= 0:
            raise ValueError("count参数必须大于0")
        if count > current_number:
            logger.warning(f"指定的期数 {count} 大于总期数 {current_number}，将爬取所有数据")
            count = current_number
        start = current_number - count + 1
        end = current_number
        logger.info(f"使用count参数：爬取最新 {count} 期数据")
    else:
        # 设置默认值
        if start is None:
            start = 1
        if end is None:
            end = current_number
        
        # 验证参数
        if start > end:
            raise ValueError("开始期数不能大于结束期数")
        if end > current_number:
            logger.warning(f"指定的结束期数 {end} 大于最新期数 {current_number}，将使用最新期数")
            end = current_number
    
    logger.info("【{}】最新一期期号：{}".format(game_name, current_number))
    logger.info("正在获取【{}】数据，期数范围：{} - {}...".format(game_name, start, end))
    
    # 创建data目录
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    data = spider(start, end, "train", tail) # 传递tail参数

    if not data.empty and os.path.exists(os.path.join(data_dir, "plw_data.csv")):
        logger.info("【{}】数据准备就绪，共{}期 (期数范围: {} - {}), 下一步可训练模型...".format(
            game_name, len(data), data['期数'].min(), data['期数'].max()))
    else:
        logger.error("数据文件不存在或数据为空！")


if __name__ == "__main__":
    if not args.name:
        raise Exception("玩法名称不能为空！")
    else:
        run(name=args.name, start=args.start, end=args.end, count=args.count, tail=args.tail)

# python get_data.py --name plw --count 1000
# python get_data.py --name plw --count 1000 --tail 500