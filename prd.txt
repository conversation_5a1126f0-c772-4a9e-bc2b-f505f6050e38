现在有5个号码，每个号码大小0-9。
数据文件使用get_data.py进行

前面5个号码有3个比值，奇偶比，大小比，质合比。
后面2个号码是有3个比值，奇偶比，大小比，质合比。


红球比数状态对照:
  奇偶比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0
  大小比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0
  质合比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0

蓝球比数状态对照:
  奇偶比: 状态0=0:2, 状态1=1:1, 状态2=2:0
  大小比: 状态0=0:2, 状态1=1:1, 状态2=2:0
  质合比: 状态0=0:2, 状态1=1:1, 状态2=2:0


预算号码的时候红球3个比值中选各选2个状态，蓝球3个比值中选各选2个状态。


回测准则：假如当前是25065期数，那么使用25065之前（包括25065期）进行选择数字。直接读取CSV中的文件对结果判断是否命中。
预测输出格式如下：
基于25065期预测第25066期:
  红球比数:
     红球奇偶比: 预测[1:4(0.592), 2:3(0.141)] -> 实际[1:4] (命中)
     红球大小比: 预测[4:1(0.361), 3:2(0.311)] -> 实际[3:2] (命中)
     红球质合比: 预测[1:4(0.592), 2:3(0.141)] -> 实际[1:4] (命中)
  蓝球比数:
     蓝球奇偶比: 预测[1:1(0.361), 2:0(0.311)] -> 实际[1:1] (命中)
     蓝球大小比: 预测[1:1(0.489), 2:0(0.170)] -> 实际[1:1] (命中)
     蓝球质合比: 预测[1:1(0.361), 2:0(0.311)] -> 实际[0:2] (未命中)

预测号码功能：
1.先使用马尔科夫链对红球的奇偶比，大小比，质合比分别进行预测。
2.对蓝球的奇偶比，大小比，质合比分别进行预测。
3.根据历史数据，优化马尔科夫链然后应用到预测号码中。
4.根据命中历史规则选出每个比值中马尔科夫概率最大的值是多少，如果大于这个值则使用马尔可夫公式进行选号，如果小于这个值则使用贝叶斯公式选号。
5.根据没有命中的号码，根据红球，蓝球分类统计出来，对这些数据进行分析，优化贝叶斯公式，进行选出2个预测值。




