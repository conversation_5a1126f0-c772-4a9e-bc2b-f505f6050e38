{"models": {"main": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview", "maxTokens": 12000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "perplexity/sonar-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "openai/gpt-4.1-mini", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "5555555", "defaultTag": "master2"}}